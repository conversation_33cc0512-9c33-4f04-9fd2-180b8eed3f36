import json
import argparse
from ecms_crawler import ECMSCrawler

def load_config(config_file: str) -> dict:
    """Load configuration from JSON file"""
    with open(config_file, 'r') as f:
        return json.load(f)

def main():
    parser = argparse.ArgumentParser(description='ECMS Document Crawler')
    parser.add_argument('--config', required=True, help='Configuration file path')
    parser.add_argument('--folders', required=True, nargs='+', help='Folder paths to crawl')
    parser.add_argument('--type', choices=['-f', '-i'], default='-f', 
                       help='Crawl type: -f (full) or -i (incremental)')
    parser.add_argument('--output', help='Output JSON file for results')
    
    args = parser.parse_args()
    
    # Load configuration
    config = load_config(args.config)
    
    # Initialize crawler
    crawler = ECMSCrawler(config)
    
    try:
        # Crawl folders
        documents = crawler.crawl_folders(
            folder_paths=args.folders,
            crawl_type=args.type,
            modified_facet=config.get('modified_facet', '')
        )
        
        print(f"Successfully crawled {len(documents)} documents")
        
        # Save results if output file specified
        if args.output:
            with open(args.output, 'w') as f:
                json.dump(documents, f, indent=2, default=str)
            print(f"Results saved to {args.output}")
            
    except Exception as e:
        print(f"Crawling failed: {e}")
        return 1
    
    return 0

if __name__ == '__main__':
    exit(main())