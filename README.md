# ECMS Document Crawler

A Python-based document crawler for OpenText Content Server (OTCS) that can crawl folders, download documents, and extract metadata.

## Features

- **Folder Crawling**: Navigate through OTCS folder structures using path-based navigation
- **Document Download**: Download documents from OTCS to local storage
- **Metadata Extraction**: Extract document metadata and categories
- **Incremental Crawling**: Support for full (-f) and incremental (-i) crawling modes
- **Watson Integration**: Configuration for Watson Discovery integration
- **Flexible Configuration**: JSON-based configuration management

## Project Structure

```
python-ecms-api-code/
├── main.py              # Main entry point
├── ecms_crawler.py      # Main crawler class
├── ecms_cilent.py       # OTCS client for API interactions
├── path_parse.py        # Path parsing utilities
├── config.json          # Configuration file
└── README.md           # This file
```

## Prerequisites

- Python 3.6 or higher
- Access to OpenText Content Server (OTCS)
- Network connectivity to the ECMS server
- Valid OTCS credentials

## Installation

1. **Clone or download the project files**

2. **Install required Python packages**:
   ```bash
   pip install requests
   ```

3. **Configure the application** (see Configuration section below)

## Configuration

Edit the `config.json` file with your ECMS server details:

```json
{
  "host": "usodcwvpwecms05.corp.intranet",
  "username": "ecmswatp",
  "password": "your-password-here",
  "scheme": "http",
  "download_path": "./downloads",
  "modified_facet": "31826615",
  "categories": ["WatsonFederal", "WatsonNonFederal", "KIT"],
  "rest_endpoint": "/livelink/cs.exe/api/v1",
  "rest_download_endpoint": "/livelink/cs.exe/api/v2",
  "qual": "/livelink",
  "crawl_folders": ["Knowledge Information Tool (KIT)/Federal-Active"],
  "watson_hostname": "vwodwexdv001",
  "watson_port": 8390,
  "watson_username": "esadmin",
  "watson_password": "your-watson-password",
  "watson_method": "add",
  "watson_col_id": "federal",
  "watson_stage_location": "E:\\Alitek\\Watson\\fed_temp\\",
  "integration_host": "https://usodcwvpwecms09.corp.intranet"
}
```

### Configuration Parameters

| Parameter | Description | Required |
|-----------|-------------|----------|
| `host` | ECMS server hostname | Yes |
| `username` | OTCS username | Yes |
| `password` | OTCS password | Yes |
| `scheme` | Protocol (http/https) | Yes |
| `download_path` | Local download directory | No |
| `modified_facet` | Modified date facet ID | No |
| `rest_endpoint` | OTCS REST API endpoint | Yes |
| `categories` | Document categories to process | No |

## Usage

### Basic Usage

```bash
python3 main.py --config config.json --folders "Knowledge Information Tool (KIT)/Federal-Active" --output results.json
```

### Command Line Arguments

- `--config`: Path to configuration file (required)
- `--folders`: One or more folder paths to crawl (required)
- `--type`: Crawl type - `-f` for full or `-i` for incremental (optional, defaults to `-f`)
- `--output`: Output JSON file for results (optional)

### Examples

**Full crawl of a single folder:**
```bash
python3 main.py --config config.json --folders "Knowledge Information Tool (KIT)/Federal-Active"
```

**Incremental crawl with output file:**
```bash
python3 main.py --config config.json --folders "Knowledge Information Tool (KIT)/Federal-Active" --type -i --output incremental_results.json
```

**Crawl multiple folders:**
```bash
python3 main.py --config config.json --folders "Folder1" "Folder2" "Folder3" --output multi_folder_results.json
```

## Folder Path Format

Folder paths use colon (`:`) as delimiter:
- `"Knowledge Information Tool (KIT):Federal-Active"`
- `"Root Folder:Subfolder:Document Folder"`

## Output

The crawler generates:

1. **Console Output**: Progress messages and document processing status
2. **Downloaded Files**: Documents saved to the configured download directory
3. **JSON Results** (if --output specified): Metadata for all processed documents

### Sample JSON Output

```json
[
  {
    "id": 12345,
    "name": "Document Name",
    "file_name": "document.pdf",
    "mime_type": "application/pdf",
    "file_size": 1024000,
    "create_date": "2023-01-01T10:00:00Z",
    "modify_date": "2023-01-02T15:30:00Z",
    "categories": {},
    "properties": {},
    "local_path": "./downloads/document.pdf",
    "downloaded": true
  }
]
```

## Network Requirements

This application requires:
- Access to the corporate network where ECMS server is hosted
- Ability to resolve internal hostnames (e.g., `usodcwvpwecms05.corp.intranet`)
- Firewall permissions for HTTP/HTTPS traffic to ECMS server

## Troubleshooting

### Common Issues

1. **Authentication Failed**
   - Verify username and password in config.json
   - Ensure OTCS credentials are valid
   - Check network connectivity to ECMS server

2. **Hostname Resolution Error**
   - Ensure you're connected to the corporate network
   - Verify the hostname in config.json is correct
   - Check DNS resolution for the ECMS server

3. **Folder Not Found**
   - Verify folder path syntax (use `:` as delimiter)
   - Ensure folder exists and is accessible
   - Check user permissions for the folder

4. **Download Failures**
   - Verify download_path directory exists and is writable
   - Check available disk space
   - Ensure user has download permissions

### Debug Mode

For detailed debugging, you can modify the code to add more verbose logging or run with Python's verbose mode:

```bash
python3 -v main.py --config config.json --folders "Your Folder Path"
```

## Security Notes

- Store passwords securely and avoid committing them to version control
- Use HTTPS when possible for secure communication
- Ensure proper access controls on downloaded files
- Follow your organization's security policies for API access

## Support

For issues related to:
- OTCS server configuration: Contact your ECMS administrator
- Network connectivity: Contact your IT support team
- Application bugs: Review the code and error messages for troubleshooting
