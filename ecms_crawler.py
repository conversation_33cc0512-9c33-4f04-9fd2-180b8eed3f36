import os
from typing import List
from .ecms_client import ECMSClient
from .path_parser import PathParser

class ECMSCrawler:
    def __init__(self, config: dict):
        self.client = ECMSClient(
            host=config['host'],
            username=config['username'], 
            password=config['password'],
            scheme=config.get('scheme', 'http')
        )
        self.download_path = config.get('download_path', './downloads')
        os.makedirs(self.download_path, exist_ok=True)
        
    def crawl_folders(self, folder_paths: List[str], crawl_type: str = "-f", 
                     modified_facet: str = "") -> List[dict]:
        """Crawl specified folders and return document information"""
        if not self.client.authenticate():
            raise Exception("Authentication failed")
            
        all_documents = []
        
        for folder_path in folder_paths:
            print(f"Crawling folder: {folder_path}")
            
            # Parse folder path to get node ID
            parser = PathParser(folder_path, ":", self.client)
            folder_id = parser.get_node_id()
            
            if not folder_id:
                print(f"Could not find folder: {folder_path}")
                continue
                
            # Get all documents in folder
            page = 1
            while True:
                doc_ids = self.client.get_child_ids(folder_id, page, crawl_type, modified_facet)
                if not doc_ids:
                    break
                    
                for doc_id in doc_ids:
                    # Get metadata
                    metadata = self.client.get_document_metadata(doc_id)
                    if metadata:
                        # Download document
                        filename = metadata.get('file_name', f'document_{doc_id}')
                        file_path = os.path.join(self.download_path, filename)
                        
                        if self.client.download_document(doc_id, file_path):
                            metadata['local_path'] = file_path
                            metadata['downloaded'] = True
                        else:
                            metadata['downloaded'] = False
                            
                        all_documents.append(metadata)
                        print(f"Processed document: {filename}")
                
                page += 1
                
        return all_documents
    
    def get_folder_contents(self, folder_path: str) -> List[dict]:
        """Get contents of a specific folder without downloading"""
        if not self.client.authenticate():
            raise Exception("Authentication failed")
            
        parser = PathParser(folder_path, ":", self.client)
        folder_id = parser.get_node_id()
        
        if not folder_id:
            return []
            
        contents = []
        page = 1
        
        while True:
            doc_ids = self.client.get_child_ids(folder_id, page)
            if not doc_ids:
                break
                
            for doc_id in doc_ids:
                metadata = self.client.get_document_metadata(doc_id)
                if metadata:
                    contents.append(metadata)
            page += 1
            
        return contents