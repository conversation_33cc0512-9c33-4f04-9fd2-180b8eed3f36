import requests
import json
import os
from datetime import datetime, timedelta
from urllib.parse import urljoin
from typing import List, Dict, Optional

class ECMSClient:
    def __init__(self, host: str, username: str, password: str, scheme: str = "http"):
        self.host = host
        self.username = username
        self.password = password
        self.scheme = scheme
        self.ticket = None
        self.session = requests.Session()
        
    def authenticate(self) -> bool:
        """Get OTCS authentication ticket"""
        auth_url = f"{self.scheme}://{self.host}/auth"
        try:
            response = self.session.post(auth_url, data={
                'username': self.username,
                'password': self.password
            })
            if response.status_code == 200:
                self.ticket = response.json().get('ticket')
                self.session.headers.update({'OTCSTICKET': self.ticket})
                return True
        except Exception as e:
            print(f"Authentication failed: {e}")
        return False
    
    def get_root_folder(self, folder_name: str) -> Optional[int]:
        """Get root folder ID by name"""
        if not self.ticket:
            return None
            
        url = f"{self.scheme}://{self.host}/volumes/141/nodes/"
        params = {
            'where_type': '0',
            'where_name': folder_name,
            'fields': 'data'
        }
        
        try:
            response = self.session.get(url, params=params)
            if response.status_code == 200:
                data = response.json()
                if data.get('data') and len(data['data']) > 0:
                    return data['data'][0]['id']
        except Exception as e:
            print(f"Error getting root folder: {e}")
        return None
    
    def get_child_ids(self, parent_id: int, page: int = 1, crawl_type: str = "-f", 
                     modified_facet: str = "") -> List[int]:
        """Get child document IDs from parent folder"""
        if not self.ticket:
            return []
            
        url = f"{self.scheme}://{self.host}/nodes/{parent_id}/nodes"
        params = {'page': page, 'limit': 100}
        
        if crawl_type == "-i" and modified_facet:
            # Add date filtering for incremental crawl
            today = datetime.now()
            yesterday = today - timedelta(days=1)
            date_filter = f"{modified_facet}:dy{yesterday.strftime('%Y%m%d')}|dy{today.strftime('%Y%m%d')}"
            params['where_facet'] = date_filter
        
        try:
            response = self.session.get(url, params=params)
            if response.status_code == 200:
                data = response.json()
                # Filter for documents (type 144)
                return [item['id'] for item in data.get('data', []) if item.get('type') == 144]
        except Exception as e:
            print(f"Error getting child IDs: {e}")
        return []
    
    def download_document(self, doc_id: int, output_path: str) -> bool:
        """Download document content"""
        if not self.ticket:
            return False
            
        download_url = f"{self.scheme}://{self.host}/nodes/{doc_id}/content?action=download"
        
        try:
            response = self.session.get(download_url, stream=True)
            if response.status_code == 200:
                with open(output_path, 'wb') as f:
                    for chunk in response.iter_content(chunk_size=8192):
                        f.write(chunk)
                return True
        except Exception as e:
            print(f"Error downloading document {doc_id}: {e}")
        return False
    
    def get_document_metadata(self, doc_id: int) -> Dict:
        """Extract document metadata and categories"""
        if not self.ticket:
            return {}
            
        url = f"{self.scheme}://{self.host}/nodes/{doc_id}"
        
        try:
            response = self.session.get(url)
            if response.status_code == 200:
                data = response.json()
                doc_data = data.get('data', {})
                
                return {
                    'id': doc_data.get('id'),
                    'name': doc_data.get('name'),
                    'file_name': doc_data.get('file_name'),
                    'mime_type': doc_data.get('mime_type'),
                    'file_size': doc_data.get('file_size'),
                    'create_date': doc_data.get('file_create_date'),
                    'modify_date': doc_data.get('file_modify_date'),
                    'categories': doc_data.get('categories', {}),
                    'properties': doc_data.get('properties', {})
                }
        except Exception as e:
            print(f"Error getting metadata for document {doc_id}: {e}")
        return {}