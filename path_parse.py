from typing import Optional

class PathParser:
    def __init__(self, path: str, delimiter: str, client):
        self.path = path
        self.delimiter = delimiter
        self.client = client
        
    def get_node_id(self) -> Optional[int]:
        """Parse path and return node ID"""
        if not self._is_valid_path():
            return None
            
        path_elements = self._get_path_elements()
        if not path_elements:
            return None
            
        # Start with root folder
        node_id = self.client.get_root_folder(path_elements[0])
        
        # Navigate through subfolders
        for i in range(1, len(path_elements)):
            if node_id is None or node_id <= 0:
                break
            node_id = self.client.get_subfolder_id(node_id, path_elements[i])
            
        return node_id
    
    def _is_valid_path(self) -> bool:
        return self.path and self.delimiter in self.path
    
    def _get_path_elements(self) -> list:
        if not self._is_valid_path():
            return []
        return [elem.strip() for elem in self.path.split(self.delimiter) if elem.strip()]